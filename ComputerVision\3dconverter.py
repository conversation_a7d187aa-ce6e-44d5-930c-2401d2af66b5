import cv2
from matplotlib import pyplot as plt
import numpy as np
from ultralytics import YOL<PERSON>
def plot_3d_model(object_points):

    # Tạo figure và axes 3D
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # Vẽ các điểm (keypoints)
    ax.scatter(object_points[:, 0], object_points[:, 1], object_points[:, 2], c='red', s=100, label='Keypoints')

    # Kết nối các điểm để tạo cấu trúc cơ thể
    # connections = [
    #     (9, 7), 
    #     (1, 3),  
    #     (6, 4),  
    #     (3, 5), 
    #     (4, 5),  
    #     (4, 10), 
    #     (5, 11), 
    #     (10, 11),
    #     (8, 6), 
    #     (5, 7), 
    #     (11, 13), 
    #     (10, 12),  
    #     (12, 14),
    #     (13, 15) 
    # ]

    # # Vẽ các đường nối
    # for connection in connections:
    #     point1 = object_points[connection[0]]
    #     point2 = object_points[connection[1]]
    #     ax.plot(
    #         [point1[0], point2[0]],  # x
    #         [point1[1], point2[1]],  # y
    #         [point1[2], point2[2]],  # z
    #         c='blue'
    #     )

    # Đặt nhãn cho các trục
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')

    # Đặt tiêu đề
    ax.set_title('3D Model of Body Keypoints')

    # Đặt giới hạn cho các trục (tùy chỉnh theo tọa độ)
    ax.set_xlim([-2, 2])
    ax.set_ylim([-2, 2])
    ax.set_zlim([-2.5, 4.5])

    # Thêm chú thích
    ax.legend()

    # Hiển thị đồ thị
    plt.show()

def get_matching_keypoints(img1_path, img2_path, model_path, K1, K2, dist_coeffs1 = None, dist_coeffs2=None):
    # Load YOLO model
    model = YOLO(model_path)
    
    # Predict trên 2 ảnh
    results1 = model(img1_path)[0]
    results2 = model(img2_path)[0]
    
    # Lists để lưu keypoints
    kpts1 = []
    kpts2 = []
    
    # Lấy keypoints từ kết quả predict
    if results1.keypoints is not None and results2.keypoints is not None:
        keypoints1 = results1.keypoints.data.numpy()  # shape: (n, 17, 3)
        keypoints2 = results2.keypoints.data.numpy()
        
        # Với mỗi người trong ảnh 1
        for person1 in keypoints1:
            for person2 in keypoints2:
                valid_pairs = []
                for i in range(len(person1)):
                    if person1[i][2] > 0.5 and person2[i][2] > 0.5:
                        valid_pairs.append(i)
                if len(valid_pairs) >= 5:
                    for idx in valid_pairs:
                        kpts1.append([person1[idx][0], person1[idx][1]])
                        kpts2.append([person2[idx][0], person2[idx][1]])
    
    # Chuyển sang numpy array
    points1 = np.float32(kpts1).reshape(-1, 1, 2)
    points2 = np.float32(kpts2).reshape(-1, 1, 2)
    
    # # Khử méo
    # points1_undistorted = cv2.undistortPoints(points1, K1, dist_coeffs1, P=K1)
    # points2_undistorted = cv2.undistortPoints(points2, K2, dist_coeffs2, P=K2)
    
    # # Chuyển lại định dạng
    points1_undistorted = points1.reshape(-1, 2)
    points2_undistorted = points2.reshape(-1, 2)
    
    return points1_undistorted, points2_undistorted

# Tham số nội tại và hệ số méo
K1 = np.array([[955.64, 0, 784.75], [0, 902.67, 1040.39], [0, 0, 1]])
K2 = np.array([[1328.61, 0, 507.68], [0, 1331.32, 951.98], [0, 0, 1]])
# dist_coeffs1 = np.array([k1_1, k2_1, p1_1, p2_1, k3_1])  # Thay bằng giá trị thực
# dist_coeffs2 = np.array([k1_2, k2_2, p1_2, p2_2, k3_2])  # Thay bằng giá trị thực

# Đường dẫn ảnh và mô hình
img1_path = "D:\\AI\\CameraCalib\\human_frame_and\\frame_001667.jpg"
img2_path = "D:\\AI\\CameraCalib\\human_frame_ip\\frame_001199.jpg"
model_path = "D:\\AI\\CameraCalib\\ComputerVision\\yolov8n-pose.pt"

# Lấy keypoints đã khử méo
points1, points2 = get_matching_keypoints(img1_path, img2_path, model_path, K1, K2)

sift = cv2.SIFT_create()
gray_1= cv2.cvtColor(cv2.imread(img1_path),cv2.COLOR_BGR2GRAY)
gray_2= cv2.cvtColor(cv2.imread(img2_path),cv2.COLOR_BGR2GRAY)

kp1 = sift.detect(gray_1, None)
kp2 = sift.detect(gray_2, None)

# Tính ma trận cơ bản
F, mask = cv2.findFundamentalMat(points1, points2, cv2.FM_RANSAC)

good_points1 = kp1[mask.ravel() == 1]
good_points2 = kp2[mask.ravel() == 1]
print(f"Số điểm tốt sau RANSAC: {len(good_points1)}")

# Tính ma trận thiết yếu
E = K2.T @ F @ K1

points1_norm = cv2.undistortPoints(
    good_points1.reshape(-1, 1, 2), K1, None
).reshape(-1, 2)
    
points2_norm = cv2.undistortPoints(
    good_points2.reshape(-1, 1, 2), K2, None
).reshape(-1, 2)

# Phân rã ma trận thiết yếu để lấy R, t
points_count, R, t, mask_pose = cv2.recoverPose(
        E, points1_norm, points2_norm
    )

print(f"Số điểm được sử dụng trong recoverPose: {points_count}")
    
    # 6. Lọc điểm cuối cùng theo mask_pose
final_points1 = good_points1[mask_pose.ravel() == 255]
final_points2 = good_points2[mask_pose.ravel() == 255]
print(f"Số điểm cuối cùng cho triangulation: {len(final_points1)}")
    
    # 7. Tạo projection matrices
proj_matrix1 = K1 @ np.hstack([np.eye(3), np.zeros((3, 1))])
proj_matrix2 = K2 @ np.hstack([R, t])

points_4d = cv2.triangulatePoints(
    proj_matrix1, proj_matrix2, 
    points1.T, points2.T
)

points_3d = points_4d[:3] / points_4d[3]
points_3d = points_3d.T
    
    # 10. Lọc điểm có độ sâu hợp lý
valid_depth = (points_3d[:, 2] > 0) & (points_3d[:, 2] < 1000)
points_3d_final = points_3d[valid_depth]

print("3D points:\n", points_3d_final)  
plot_3d_model(points_3d_final)
z_range = np.max(points_3d[2]) - np.min(points_3d[2])
print("Z range:", z_range)
hip_left = points_3d[:, 11]
hip_right = points_3d[:, 12]
real_shoulder_dist = 0.4  # mét
current_dist = np.linalg.norm(hip_left - hip_right)
scale_factor = real_shoulder_dist / current_dist
points_3d = points_3d * scale_factor

hip_left = points_3d.T[11]
hip_right = points_3d.T[12]
ankle_left = points_3d.T[13]
ankle_right = points_3d.T[14]

hip_center = (hip_left + hip_right) / 2
ankle_center = (ankle_left + ankle_right) / 2
up_vector = hip_center - ankle_center
up_vector /= np.linalg.norm(up_vector)

from scipy.spatial.transform import Rotation as R

target = np.array([0, 0, 1])
rotation_axis = np.cross(up_vector, target)
angle = np.arccos(np.clip(np.dot(up_vector, target), -1, 1))
rotation = R.from_rotvec(rotation_axis * angle)
rotated_points_3d = rotation.apply(points_3d.T)  # shape (N, 3)

hip_center_rotated = rotation.apply(hip_center.reshape(1, -1))[0]
rotated_points_3d -= hip_center_rotated

# print("Rotation matrix R:\n", R)
# print("Translation vector t:\n", t)
# print("3D points aligned upright:\n", rotated_points_3d)
# z_range = np.max(points_3d[2]) - np.min(points_3d[2])
# print("Z range:", z_range)


