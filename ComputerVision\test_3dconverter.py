"""
Test script cho 3D converter
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ComputerVision.3dconverter import complete_3d_reconstruction_workflow
import numpy as np

def test_3d_reconstruction():
    """
    Test function cho 3D reconstruction workflow
    """
    print("=== TEST 3D RECONSTRUCTION WORKFLOW ===")
    
    # Thông số camera (có thể điều chỉnh theo camera thực tế)
    K1 = np.array([[955.64, 0, 784.75], [0, 902.67, 1040.39], [0, 0, 1]])
    K2 = np.array([[1328.61, 0, 507.68], [0, 1331.32, 951.98], [0, 0, 1]])

    # Đường dẫn file (điều chỉnh theo đường dẫn thực tế)
    img1_path = "human_frame_and/frame_001608.jpg"
    img2_path = "human_frame_ip/frame_001140.jpg"
    model_path = "ComputerVision/yolov8n-pose.pt"
    
    # <PERSON><PERSON><PERSON> tra file tồn tại
    files_to_check = [img1_path, img2_path, model_path]
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"Cảnh báo: File không tồn tại: {file_path}")
            print("Vui lòng điều chỉnh đường dẫn file trong test_3dconverter.py")
            return False
    
    try:
        # Chạy quy trình hoàn chỉnh
        aligned_points, idx_map, transformation_info = complete_3d_reconstruction_workflow(
            img1_path, img2_path, model_path, K1, K2, 
            confidence_thresh=0.5, visualize=True
        )
        
        print("\n=== KẾT QUẢ TEST ===")
        print(f"✓ Thành công tái dựng {len(aligned_points)} điểm 3D")
        print(f"✓ Keypoints được sử dụng: {idx_map}")
        print(f"✓ Góc xoay: {transformation_info.get('angle_degrees', 0):.2f} độ")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi trong quá trình test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_3d_reconstruction()
    if success:
        print("\n🎉 Test thành công!")
    else:
        print("\n❌ Test thất bại!")
