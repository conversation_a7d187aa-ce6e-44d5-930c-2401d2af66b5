# 3D Human Pose Reconstruction Workflow

## <PERSON><PERSON> <PERSON>ả
<PERSON>le `3dconverter.py` đ<PERSON> đư<PERSON>c hoàn thiện theo workflow yêu cầu:

1. **<PERSON><PERSON><PERSON> đúng thứ tự khớp theo chuẩn YOLO** - Trích xu<PERSON>t keypoints theo đúng thứ tự 17 điểm chuẩn YOLO
2. **Tái dựng 3D** - Sử dụng triangulation để tính toán tọa độ 3D
3. **X<PERSON><PERSON> bộ xương đứng thẳng** - Căn chỉnh theo trục Y dương
4. **Dịch chuyển sao cho hông là gốc tọa độ** - Đặt trung tâm hông tại (0,0,0)

## C<PERSON>u trúc YOLO Keypoints (17 điểm)

```
0:  nose           - M<PERSON><PERSON>
1:  left_eye       - <PERSON><PERSON><PERSON> trái  
2:  right_eye      - <PERSON><PERSON><PERSON> phải
3:  left_ear       - <PERSON> trái
4:  right_ear      - <PERSON> phải
5:  left_shoulder  - <PERSON><PERSON> trá<PERSON>
6:  right_shoulder - <PERSON><PERSON> ph<PERSON>
7:  left_elbow     - Khuỷu tay trái
8:  right_elbow    - Khuỷu tay phải
9:  left_wrist     - <PERSON><PERSON> tay trái
10: right_wrist    - <PERSON><PERSON> tay phải
11: left_hip       - Hông trái
12: right_hip      - Hông phải
13: left_knee      - Đầu gối trái
14: right_knee     - Đầu gối phải
15: left_ankle     - Mắt cá chân trái
16: right_ankle    - Mắt cá chân phải
```

## Sử dụng

### Cách 1: Sử dụng function hoàn chỉnh (Khuyến nghị)

```python
from ComputerVision.3dconverter import complete_3d_reconstruction_workflow
import numpy as np

# Ma trận camera
K1 = np.array([[955.64, 0, 784.75], [0, 902.67, 1040.39], [0, 0, 1]])
K2 = np.array([[1328.61, 0, 507.68], [0, 1331.32, 951.98], [0, 0, 1]])

# Đường dẫn file
img1_path = "path/to/image1.jpg"
img2_path = "path/to/image2.jpg" 
model_path = "path/to/yolov8n-pose.pt"

# Chạy quy trình hoàn chỉnh
aligned_points, idx_map, transformation_info = complete_3d_reconstruction_workflow(
    img1_path, img2_path, model_path, K1, K2,
    confidence_thresh=0.5, visualize=True
)
```

### Cách 2: Sử dụng từng bước

```python
from ComputerVision.3dconverter import (
    reconstruct_3d, 
    align_pose_to_standard,
    visualize_3d_pose
)

# Bước 1: Tái dựng 3D
points_3d, idx_map = reconstruct_3d(img1_path, img2_path, model_path, K1, K2)

# Bước 2: Căn chỉnh pose
aligned_points, transformation_info = align_pose_to_standard(points_3d, idx_map)

# Bước 3: Hiển thị kết quả
visualize_3d_pose(aligned_points, idx_map, "3D Human Pose")
```

## Các Function Chính

### `get_yolo_keypoint_names()`
Trả về danh sách tên keypoints theo thứ tự chuẩn YOLO (17 điểm)

### `get_ordered_matched_keypoints()`
Trích xuất keypoints từ 2 ảnh theo đúng thứ tự YOLO, chỉ giữ lại những điểm có confidence > threshold

### `reconstruct_3d()`
Thực hiện triangulation để tái dựng tọa độ 3D từ 2 view

### `align_pose_to_standard()`
Căn chỉnh bộ xương:
- Xoay để đứng thẳng theo trục Y
- Dịch chuyển để hông là gốc tọa độ

### `visualize_3d_pose()`
Hiển thị bộ xương 3D với các kết nối xương theo chuẩn YOLO

### `complete_3d_reconstruction_workflow()`
Function tổng hợp thực hiện toàn bộ workflow

## Tham số

- `confidence_thresh`: Ngưỡng confidence cho keypoints (mặc định: 0.5)
- `visualize`: Có hiển thị kết quả 3D không (mặc định: True)
- `K1, K2`: Ma trận camera intrinsic cho 2 camera

## Kết quả

- `aligned_points`: Tọa độ 3D đã căn chỉnh (N, 3) với hông tại gốc tọa độ
- `idx_map`: Danh sách chỉ số keypoints theo thứ tự YOLO được sử dụng
- `transformation_info`: Thông tin về phép biến đổi (góc xoay, vector dịch chuyển, etc.)

## Test

Chạy file test:
```bash
python ComputerVision/test_3dconverter.py
```

Hoặc chạy trực tiếp:
```bash
python ComputerVision/3dconverter.py
```

## Yêu cầu

```bash
pip install opencv-python numpy matplotlib ultralytics scipy
```

## Lưu ý

- Cần có ít nhất 5 keypoints khớp giữa 2 ảnh để thực hiện triangulation
- Để có kết quả tốt nhất, cần có các keypoints quan trọng: hông trái/phải (11,12) và mắt cá chân trái/phải (15,16)
- Nếu thiếu các keypoints quan trọng, hệ thống sẽ tự động chuyển sang phương pháp căn chỉnh thay thế
- Kết quả cuối cùng sẽ có hông tại gốc tọa độ (0,0,0) và bộ xương đứng thẳng theo trục Y
